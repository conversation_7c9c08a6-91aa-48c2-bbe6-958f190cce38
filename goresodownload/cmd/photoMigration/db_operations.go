package main

import (
	"context"
	"fmt"
	"time"

	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// needToProcess determines whether a property needs to be processed
// Returns false if the property already has phoLH field (already migrated)
func needToProcess(prop bson.M) bool {
	// Skip if already processed (has phoLH field)
	if prop["phoLH"] != nil {
		return false
	}

	src, ok := prop["src"].(string)
	if !ok {
		return false
	}

	// For TRB and DDF, check pho field
	if src == SRC_TRB || src == SRC_DDF {
		if pho, ok := prop["pho"]; ok {
			if phoInt, ok := pho.(int32); ok && phoInt > 0 {
				return true
			}
			if phoInt, ok := pho.(int); ok && phoInt > 0 {
				return true
			}
		}
	}

	// For OTW and CLG, check phoIDs field
	if src == SRC_OTW || src == SRC_CLG {
		if phoIDs, ok := prop["phoIDs"]; ok && phoIDs != nil {
			return true
		}
	}

	return false
}

// queryProps queries properties that need to be processed
func queryProps(config Config) ([]bson.M, error) {
	collection := gomongo.Coll("listing", "properties")
	if collection == nil {
		return nil, fmt.Errorf("failed to get properties collection")
	}

	// Build query filter
	filter := bson.M{}
	if config.MT != nil {
		filter["mt"] = bson.M{"$gte": *config.MT}
	}

	// Build projection to include necessary fields
	projection := bson.M{
		"mt":     1,
		"sid":    1,
		"src":    1,
		"ts":     1,
		"board":  1,
		"pho":    1,
		"phoIDs": 1,
		"orgId":  1,
		"phoLH":  1,
	}

	// Build query options with reverse chronological order
	opts := options.Find().
		SetProjection(projection).
		SetSort(bson.M{"ts": -1})

	ctx := context.Background()
	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to query properties: %w", err)
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err := cursor.All(ctx, &results); err != nil {
		return nil, fmt.Errorf("failed to decode results: %w", err)
	}

	golog.Info("Queried properties", "total", len(results))
	return results, nil
}

// insertImmigrationLog inserts migration log into database
func insertImmigrationLog(result MigrationResult) error {
	var collectionName string
	if result.Disk == "ca6" {
		collectionName = "photo_immigration_ca6"
	} else {
		collectionName = "photo_immigration_ca7"
	}

	collection := gomongo.Coll("listing", collectionName)
	if collection == nil {
		return fmt.Errorf("failed to get %s collection", collectionName)
	}

	ctx := context.Background()
	_, err := collection.InsertOne(ctx, result)
	if err != nil {
		return fmt.Errorf("failed to insert immigration log: %w", err)
	}

	return nil
}

// updatePropDB updates the properties collection with new phoHL and tnHL fields
func updatePropDB(propID string, phoHL []int32, tnHL int32) error {
	collection := gomongo.Coll("listing", "properties")
	if collection == nil {
		return fmt.Errorf("failed to get properties collection")
	}

	filter := bson.M{"_id": propID}
	update := bson.M{
		"$set": bson.M{
			"phoHL": phoHL,
			"tnHL":  tnHL,
		},
	}

	ctx := context.Background()
	result, err := collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update prop: %w", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("no prop found with id: %s", propID)
	}

	golog.Info("Updated prop", "propID", propID, "phoHL", len(phoHL), "tnHL", tnHL)
	return nil
}

// updateRniDB updates the corresponding RNI collection with thumbnail hash
func updateRniDB(prop bson.M, tnHL int32) error {
	src, ok := prop["src"].(string)
	if !ok {
		return fmt.Errorf("invalid src field")
	}

	var collectionName string
	var queryField string
	var queryValue interface{}

	// Determine collection and query parameters based on source type
	switch src {
	case SRC_TRB:
		collectionName = "mls_treb_master_records"
		queryField = "_id"
		queryValue = prop["_id"]
	case SRC_DDF:
		collectionName = "mls_crea_ddf_records"
		queryField = "_id"
		queryValue = prop["_id"]
	case SRC_OTW:
		collectionName = "mls_oreb_master_records"
		queryField = "_id"
		queryValue = prop["orgId"]
	case SRC_CLG:
		collectionName = "mls_creb_master_records"
		queryField = "_id"
		queryValue = prop["orgId"]
	default:
		return fmt.Errorf("unsupported src type: %s", src)
	}

	collection := gomongo.Coll("rni", collectionName)
	if collection == nil {
		return fmt.Errorf("failed to get %s collection", collectionName)
	}

	filter := bson.M{queryField: queryValue}
	update := bson.M{
		"$set": bson.M{
			"tnHL": tnHL,
		},
	}

	ctx := context.Background()
	result, err := collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update rni: %w", err)
	}

	if result.MatchedCount == 0 {
		golog.Warn("No rni record found", "collection", collectionName, "queryField", queryField, "queryValue", queryValue)
	} else {
		golog.Info("Updated rni", "collection", collectionName, "queryValue", queryValue, "tnHL", tnHL)
	}

	return nil
}

// updateDirStats updates directory statistics for the levelstore
func updateDirStats(prop bson.M, l1, l2 string, entityCount, fileCount int) error {
	src, ok := prop["src"].(string)
	if !ok {
		return fmt.Errorf("invalid src field")
	}

	var store *levelStore.DirKeyStore
	switch src {
	case SRC_TRB:
		store = storeTRB
	case SRC_DDF:
		store = storeDDF
	case SRC_OTW:
		store = storeOTW
	case SRC_CLG:
		store = storeCLG
	default:
		return fmt.Errorf("unsupported src type: %s", src)
	}

	if store == nil {
		return fmt.Errorf("store not initialized for src: %s", src)
	}

	store.AddDirStats(l1, l2, entityCount, fileCount)
	golog.Info("Updated dir stats", "src", src, "l1", l1, "l2", l2, "entities", entityCount, "files", fileCount)
	return nil
}

// updateDB updates both properties and RNI collections
func updateDB(params DBUpdateParams) error {
	propID, ok := params.Prop["_id"].(string)
	if !ok {
		return fmt.Errorf("invalid prop _id")
	}

	// Update properties collection
	if err := updatePropDB(propID, params.PhoHL, params.TnHL); err != nil {
		return fmt.Errorf("failed to update prop DB: %w", err)
	}

	// Update RNI collection
	if err := updateRniDB(params.Prop, params.TnHL); err != nil {
		return fmt.Errorf("failed to update rni DB: %w", err)
	}

	return nil
}
