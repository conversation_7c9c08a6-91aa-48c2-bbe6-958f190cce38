package main

import (
	"fmt"
	"image"
	"os"
	"path/filepath"

	golog "github.com/real-rm/golog"
	gofile "github.com/real-rm/gofile"
	levelStore "github.com/real-rm/gofile/golevelstore"
	"go.mongodb.org/mongo-driver/bson"
)

// fileExists checks if a file exists at the given path
func fileExists(path string) bool {
	_, err := os.Stat(path)
	return err == nil
}

// ensureDir ensures that the directory for the given file path exists
func ensureDir(path string) error {
	dir := filepath.Dir(path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory %s: %w", dir, err)
	}
	return nil
}

// renameFile renames a file from source to destination
func renameFile(src, dst string) error {
	// Ensure destination directory exists
	if err := ensureDir(dst); err != nil {
		return err
	}

	if err := os.Rename(src, dst); err != nil {
		return fmt.Errorf("failed to rename %s to %s: %w", src, dst, err)
	}

	golog.Info("File renamed successfully", "src", src, "dst", dst)
	return nil
}

// copyFile copies a file from source to destination
func copyFile(src, dst string) error {
	// Ensure destination directory exists
	if err := ensureDir(dst); err != nil {
		return err
	}

	// Open source file
	srcFile, err := os.Open(src)
	if err != nil {
		return fmt.Errorf("failed to open source file %s: %w", src, err)
	}
	defer srcFile.Close()

	// Create destination file
	dstFile, err := os.Create(dst)
	if err != nil {
		return fmt.Errorf("failed to create destination file %s: %w", dst, err)
	}
	defer dstFile.Close()

	// Copy content
	if _, err := srcFile.WriteTo(dstFile); err != nil {
		return fmt.Errorf("failed to copy content from %s to %s: %w", src, dst, err)
	}

	golog.Info("File copied successfully", "src", src, "dst", dst)
	return nil
}

// renameOrCopy attempts to rename file, falls back to copy if rename fails
func renameOrCopy(srcPath, dstPath, disk string) (string, error) {
	// First attempt to rename (move within same disk)
	if err := renameFile(srcPath, dstPath); err == nil {
		return STATUS_RENAMED, nil
	}

	// If ca6 disk, try to copy from ca7 as fallback
	if disk == "ca6" {
		// Construct ca7 path by replacing ca6 prefix with ca7
		ca7SrcPath := srcPath
		if len(srcPath) >= len(OLD_CA6_PATH) && srcPath[:len(OLD_CA6_PATH)] == OLD_CA6_PATH {
			ca7SrcPath = OLD_CA7_PATH + srcPath[len(OLD_CA6_PATH):]
		}

		if fileExists(ca7SrcPath) {
			if err := copyFile(ca7SrcPath, dstPath); err == nil {
				return STATUS_COPIED, nil
			} else {
				return STATUS_COPY_FAILED, err
			}
		}
	}

	return STATUS_NOT_FOUND, fmt.Errorf("file not found: %s", srcPath)
}

// createThumbnail creates a thumbnail from the original image
func createThumbnail(originalPath, thumbnailPath string) (int32, error) {
	// Check if original file exists
	if !fileExists(originalPath) {
		return 0, fmt.Errorf("original file not found: %s", originalPath)
	}

	// Load and resize image using gofile package
	img, err := loadAndResizeImage(originalPath, THUMBNAIL_WIDTH, THUMBNAIL_HEIGHT)
	if err != nil {
		return 0, fmt.Errorf("failed to load and resize image: %w", err)
	}

	// Ensure thumbnail directory exists
	if err := ensureDir(thumbnailPath); err != nil {
		return 0, err
	}

	// Save thumbnail as JPEG
	savedPath, err := gofile.SaveImage(img, thumbnailPath, false) // false = JPEG format
	if err != nil {
		return 0, fmt.Errorf("failed to save thumbnail: %w", err)
	}

	// Calculate thumbnail hash (original path + "-t")
	thumbKey := originalPath + "-t"
	hash := levelStore.MurmurToInt32(thumbKey)

	golog.Info("Thumbnail created", "original", originalPath, "thumbnail", savedPath, "hash", hash)
	return hash, nil
}

// loadAndResizeImage loads an image from file and resizes it
func loadAndResizeImage(imagePath string, width, height int) (image.Image, error) {
	// Open image file
	file, err := os.Open(imagePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open image file: %w", err)
	}
	defer file.Close()

	// Decode image
	img, _, err := image.Decode(file)
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %w", err)
	}

	// Resize image maintaining aspect ratio
	resizedImg := resizeImage(img, width, height)
	
	return resizedImg, nil
}

// resizeImage resizes an image maintaining aspect ratio
func resizeImage(src image.Image, maxWidth, maxHeight int) image.Image {
	// Get original dimensions
	bounds := src.Bounds()
	srcWidth := bounds.Dx()
	srcHeight := bounds.Dy()

	// Calculate scale factor maintaining aspect ratio
	scaleX := float64(maxWidth) / float64(srcWidth)
	scaleY := float64(maxHeight) / float64(srcHeight)
	scale := scaleX
	if scaleY < scaleX {
		scale = scaleY
	}

	// If no scaling needed, return original
	if scale >= 1.0 {
		return src
	}

	// Calculate new dimensions
	newWidth := int(float64(srcWidth) * scale)
	newHeight := int(float64(srcHeight) * scale)

	// Create new image
	dst := image.NewRGBA(image.Rect(0, 0, newWidth, newHeight))

	// Simple nearest-neighbor interpolation scaling
	for y := 0; y < newHeight; y++ {
		for x := 0; x < newWidth; x++ {
			srcX := int(float64(x) / scale)
			srcY := int(float64(y) / scale)
			if srcX >= srcWidth {
				srcX = srcWidth - 1
			}
			if srcY >= srcHeight {
				srcY = srcHeight - 1
			}
			dst.Set(x, y, src.At(srcX, srcY))
		}
	}

	return dst
}

// processImageFile processes a single image file
func processImageFile(params PathBuildParams, config Config) (*ImageInfo, error) {
	// Build original relative path
	relativePath, err := buildOriginalPath(params)
	if err != nil {
		return nil, fmt.Errorf("failed to build original path: %w", err)
	}

	// Build full source path
	var basePath string
	if config.Disk == "ca6" {
		basePath = OLD_CA6_PATH
	} else {
		basePath = OLD_CA7_PATH
	}
	srcPath := filepath.Join(basePath, relativePath)

	// Build new path using levelstore
	newPath, err := buildNewPath(params.Prop, relativePath)
	if err != nil {
		return nil, fmt.Errorf("failed to build new path: %w", err)
	}

	// Build full destination path
	var newBasePath string
	if config.Disk == "ca6" {
		newBasePath = NEW_CA6_PATH
	} else {
		newBasePath = NEW_CA7_PATH
	}
	dstPath := filepath.Join(newBasePath, newPath)

	// Calculate hash and base62 for the relative path
	hash := levelStore.MurmurToInt32(relativePath)
	base62, err := levelStore.Int32ToBase62(hash)
	if err != nil {
		return nil, fmt.Errorf("failed to generate base62: %w", err)
	}

	imageInfo := &ImageInfo{
		OriginalPath: srcPath,
		NewPath:      dstPath,
		Hash:         hash,
		Base62:       base62,
	}

	// Perform actual file operations if not in dry run mode
	if !config.DryRun {
		status, err := renameOrCopy(srcPath, dstPath, config.Disk)
		if err != nil {
			return imageInfo, fmt.Errorf("file operation failed: %w", err)
		}
		golog.Info("File processed", "status", status, "src", srcPath, "dst", dstPath)
	} else {
		golog.Info("DRY RUN: Would process file", "src", srcPath, "dst", dstPath)
	}

	return imageInfo, nil
}
