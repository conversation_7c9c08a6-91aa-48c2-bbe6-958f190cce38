package main

import (
	"fmt"
	"strconv"
	"time"

	golog "github.com/real-rm/golog"
	levelStore "github.com/real-rm/gofile/golevelstore"
	"go.mongodb.org/mongo-driver/bson"
)

// migrateDisk performs migration for the specified disk
func migrateDisk(disk string, config Config) error {
	golog.Info("Starting migration", "disk", disk, "dryRun", config.DryRun)

	// Query properties that need processing
	props, err := queryProps(config)
	if err != nil {
		return fmt.Errorf("failed to query properties: %w", err)
	}

	totalProps := len(props)
	processedCount := 0
	successCount := 0
	failedCount := 0

	golog.Info("Found properties to process", "total", totalProps)

	// Process each property
	for i, prop := range props {
		golog.Info("Processing property", "index", i+1, "total", totalProps, "propID", prop["_id"])

		// Check if property needs processing
		if !needToProcess(prop) {
			golog.Info("Skipping property - already processed or no images", "propID", prop["_id"])
			continue
		}

		// Process the property
		result, err := processProperty(prop, config)
		if err != nil {
			golog.Error("Failed to process property", "propID", prop["_id"], "error", err)
			failedCount++
			
			// Record failure in migration log
			migrationResult := MigrationResult{
				PropID:    fmt.Sprintf("%v", prop["_id"]),
				MT:        getTimeField(prop, "mt"),
				TS:        getTimeField(prop, "ts"),
				SrcPath:   "",
				DstPath:   "",
				Status:    "processing_failed",
				ErrMsg:    err.Error(),
				Disk:      disk,
				CreatedAt: time.Now(),
			}
			
			if !config.DryRun {
				if logErr := insertImmigrationLog(migrationResult); logErr != nil {
					golog.Error("Failed to insert error log", "error", logErr)
				}
			}
			continue
		}

		processedCount++
		if result.Success > 0 {
			successCount++
		}

		golog.Info("Property processed", 
			"propID", prop["_id"], 
			"images", result.ImageCount, 
			"success", result.Success, 
			"failed", result.Failed)
	}

	golog.Info("Migration completed", 
		"disk", disk, 
		"total", totalProps, 
		"processed", processedCount, 
		"success", successCount, 
		"failed", failedCount)

	return nil
}

// processProperty processes a single property and all its images
func processProperty(prop bson.M, config Config) (*PropertyResult, error) {
	propID := fmt.Sprintf("%v", prop["_id"])
	src, ok := prop["src"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid src field")
	}

	result := &PropertyResult{
		PropID:     propID,
		Processed:  true,
		ImageCount: 0,
		Success:    0,
		Failed:     0,
		Errors:     []error{},
	}

	var imageInfos []*ImageInfo
	var phoHL []int32
	var tnHL int32

	// Process images based on source type
	switch src {
	case SRC_TRB, SRC_DDF:
		infos, err := processTRBDDFImages(prop, config)
		if err != nil {
			return result, err
		}
		imageInfos = infos
	case SRC_OTW, SRC_CLG:
		infos, err := processOTWCLGImages(prop, config)
		if err != nil {
			return result, err
		}
		imageInfos = infos
	default:
		return result, fmt.Errorf("unsupported src type: %s", src)
	}

	result.ImageCount = len(imageInfos)

	// Process each image and collect hashes
	for i, imageInfo := range imageInfos {
		if imageInfo != nil {
			phoHL = append(phoHL, imageInfo.Hash)
			result.Success++

			// Generate thumbnail for the first image
			if i == 0 && !config.DryRun {
				thumbHash, err := createThumbnail(imageInfo.OriginalPath, imageInfo.NewPath)
				if err != nil {
					golog.Error("Failed to create thumbnail", "error", err)
					// Use original image hash as fallback for thumbnail
					tnHL = imageInfo.Hash
				} else {
					tnHL = thumbHash
				}
			} else if i == 0 {
				// In dry run mode, simulate thumbnail hash
				thumbKey := imageInfo.OriginalPath + "-t"
				tnHL = levelStore.MurmurToInt32(thumbKey)
			}
		} else {
			result.Failed++
		}
	}

	// Update database if any images were successfully processed
	if result.Success > 0 && !config.DryRun {
		updateParams := DBUpdateParams{
			Prop:  prop,
			PhoHL: phoHL,
			TnHL:  tnHL,
		}

		if err := updateDB(updateParams); err != nil {
			return result, fmt.Errorf("failed to update database: %w", err)
		}

		// Update directory statistics
		if err := updateDirectoryStats(prop, len(imageInfos)); err != nil {
			golog.Error("Failed to update directory stats", "error", err)
			// Directory stats failure doesn't affect main process
		}
	}

	return result, nil
}

// processTRBDDFImages processes images for TRB and DDF source types
func processTRBDDFImages(prop bson.M, config Config) ([]*ImageInfo, error) {
	pho, ok := prop["pho"]
	if !ok {
		return nil, fmt.Errorf("pho field not found")
	}

	var phoCount int
	switch v := pho.(type) {
	case int:
		phoCount = v
	case int32:
		phoCount = int(v)
	case int64:
		phoCount = int(v)
	default:
		return nil, fmt.Errorf("invalid pho field type")
	}

	if phoCount <= 0 {
		return nil, fmt.Errorf("invalid pho count: %d", phoCount)
	}

	var imageInfos []*ImageInfo

	// Process each image (numbered from 1 to phoCount)
	for i := 1; i <= phoCount; i++ {
		params := PathBuildParams{
			Prop:     prop,
			Src:      prop["src"].(string),
			Sid:      prop["sid"].(string),
			ImageNum: i,
		}

		imageInfo, err := processImageFile(params, config)
		if err != nil {
			golog.Error("Failed to process image", "propID", prop["_id"], "imageNum", i, "error", err)
			
			// Record failed image in migration log
			migrationResult := MigrationResult{
				PropID:    fmt.Sprintf("%v", prop["_id"]),
				MT:        getTimeField(prop, "mt"),
				TS:        getTimeField(prop, "ts"),
				SrcPath:   "",
				DstPath:   "",
				Status:    STATUS_RENAME_FAILED,
				ErrMsg:    err.Error(),
				Disk:      config.Disk,
				CreatedAt: time.Now(),
			}
			
			if !config.DryRun {
				if logErr := insertImmigrationLog(migrationResult); logErr != nil {
					golog.Error("Failed to insert error log", "error", logErr)
				}
			}
			
			imageInfos = append(imageInfos, nil)
			continue
		}

		imageInfos = append(imageInfos, imageInfo)
	}

	return imageInfos, nil
}

// processOTWCLGImages processes images for OTW and CLG source types
func processOTWCLGImages(prop bson.M, config Config) ([]*ImageInfo, error) {
	phoIDs, ok := prop["phoIDs"]
	if !ok {
		return nil, fmt.Errorf("phoIDs field not found")
	}

	phoIDsSlice, ok := phoIDs.([]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid phoIDs field type")
	}

	if len(phoIDsSlice) == 0 {
		return nil, fmt.Errorf("empty phoIDs")
	}

	var imageInfos []*ImageInfo

	// Process each image ID
	for _, phoID := range phoIDsSlice {
		imageID := fmt.Sprintf("%v", phoID)
		
		params := PathBuildParams{
			Prop:    prop,
			Src:     prop["src"].(string),
			Sid:     prop["sid"].(string),
			ImageID: imageID,
		}

		imageInfo, err := processImageFile(params, config)
		if err != nil {
			golog.Error("Failed to process image", "propID", prop["_id"], "imageID", imageID, "error", err)
			
			// Record failed image in migration log
			migrationResult := MigrationResult{
				PropID:    fmt.Sprintf("%v", prop["_id"]),
				MT:        getTimeField(prop, "mt"),
				TS:        getTimeField(prop, "ts"),
				SrcPath:   "",
				DstPath:   "",
				Status:    STATUS_RENAME_FAILED,
				ErrMsg:    err.Error(),
				Disk:      config.Disk,
				CreatedAt: time.Now(),
			}
			
			if !config.DryRun {
				if logErr := insertImmigrationLog(migrationResult); logErr != nil {
					golog.Error("Failed to insert error log", "error", logErr)
				}
			}
			
			imageInfos = append(imageInfos, nil)
			continue
		}

		imageInfos = append(imageInfos, imageInfo)
	}

	return imageInfos, nil
}

// updateDirectoryStats updates directory statistics for levelstore
func updateDirectoryStats(prop bson.M, fileCount int) error {
	// Extract required fields
	ts, ok := prop["ts"].(time.Time)
	if !ok {
		return fmt.Errorf("invalid ts field")
	}

	src, ok := prop["src"].(string)
	if !ok {
		return fmt.Errorf("invalid src field")
	}

	sid, ok := prop["sid"].(string)
	if !ok {
		return fmt.Errorf("invalid sid field")
	}

	// Get L1 and L2 directories
	l1, l2, err := levelStore.GetL1L2Separate(ts, src, sid)
	if err != nil {
		return fmt.Errorf("failed to get L1L2: %w", err)
	}

	return updateDirStats(prop, l1, l2, 1, fileCount)
}

// getTimeField safely extracts time field from property
func getTimeField(prop bson.M, fieldName string) time.Time {
	if val, ok := prop[fieldName].(time.Time); ok {
		return val
	}
	return time.Time{}
}
